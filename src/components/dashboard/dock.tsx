import { ROUTES } from "@/config/site";
import Image from "next/image";
import Link from "next/link";

type NavLinkProps = {
  href: string;
  icon: string;
  label: string;
};

const NavLink = ({ href, icon, label }: NavLinkProps) => {
  return (
    <li className="pb-3 pt-6 text-typography-200">
      <Link
        href={href}
        className="flex flex-col items-center justify-center gap-[0.44rem]"
      >
        <Image
          src={icon}
          alt={label}
          width={50}
          height={50}
          className="size-[1.45831rem]"
        />
        <span className="text-[0.58331rem] font-semibold text-typography-200">
          {label}
        </span>
      </Link>
    </li>
  );
};

const Dock = () => {
  return (
    <div className="w-full fixed bottom-0 pt-2">
      <nav className="relative isolate bg-[url('/images/dock-bg.svg')] bg-cover w-full bg-no-repeat bg-center">
        <ul className="grid grid-cols-5">
          <NavLink
            href={ROUTES.DASHBOARD.BASE}
            icon="/icons/assets.svg"
            label="Assets"
          />

          <NavLink
            href={ROUTES.DASHBOARD.ORDERS}
            icon="/icons/orders.svg"
            label="Orders"
          />

          <li className="">
            <Link
              href="/"
              className="flex justify-center relative w-full h-full bg-no-repeat bg-bottom"
            >
              <Image
                src="/images/dock-main.png"
                alt="Dock"
                width={1000}
                quality={100}
                height={1000}
                className="w-[7rem] h-[8.6875rem] absolute scale-x-110 lg:scale-x-100 -translate-y-4"
              />
            </Link>
          </li>

          <NavLink
            href={ROUTES.DASHBOARD.REVIEWS}
            icon="/icons/review.svg"
            label="Reviews"
          />

          <NavLink
            href={ROUTES.DASHBOARD.WALLETS}
            icon="/icons/affiliate.svg"
            label="Affiliate"
          />
        </ul>
      </nav>
    </div>
  );
};

export default Dock;
