"use client";
import useUserStore from "@/store/user.store";
import Balance from "./balance";
import Orders from "./orders";
import UserInfo from "./user-info";
import { useAccount } from "wagmi";
import ConnectWallet from "./ConnectWallet";

const DashboardBase = () => {
  const { isLoggedIn, user } = useUserStore();

  const { isConnected, address, chainId } = useAccount();

  const connected =
    isConnected &&
    isLoggedIn &&
    address?.toLowerCase() == user?.walletAddress?.toLowerCase() &&
    chainId === user?.chainId;

  return (
    <>
      {connected ? (
        <div className="space-y-3 flex flex-col overflow-y-auto">
          <section>
            <UserInfo />
          </section>
          <section>
            <Balance />
          </section>
          <section>
            <Orders />
          </section>
        </div>
      ) : (
        <ConnectWallet />
      )}
    </>
  );
};

export default DashboardBase;
